import settings from "./settings.js";

export function post(url, data, success, error) {
  if (typeof data == "function") {
    error = success;
    success = data;
    data = null;
  }
  if (typeof error == "boolean" && error) wx.showLoading();
  const token = wx.getStorageSync("ZY_TOKEN") || "";

  wx.request({
    method: "POST",
    url: settings.api_host + url,
    header: {
      "zy-token": token,
      appId: settings.api_id,
      appSecret: settings.api_secret,
    },
    contentType: "application/json; charset=utf-8",
    data: data ? JSON.stringify(data) : null,
    dataType: "json",
    success: function (res) {
      if (typeof error == "boolean" && error) wx.hideLoading();


      if (typeof success == "function") success(res.data);
    },
    error: function (res) {
      if (typeof error == "boolean" && error) wx.hideLoading();
      if (typeof error == "function") error(res);
    },
  });
}

export function message(msg) {
  wx.showModal({ title: "系统提醒", content: msg });
}

export function ok(msg, func) {
  if (typeof func == "function")
    wx.showModal({
      title: "系统提示",
      content: msg,
      showCancel: false,
      complete: func,
    });
  else if (func === "back")
    wx.showModal({
      title: "系统提示",
      content: msg,
      showCancel: false,
      complete: () => {
        wx.navigateBack();
      },
    });
  else wx.showToast({ title: msg || "成功", icon: "success" });
}

export function error(msg, func) {
  if (typeof func == "function")
    wx.showModal({
      title: "系统提示",
      content: msg,
      showCancel: false,
      complete: func,
    });
  else if (func === "back")
    wx.showModal({
      title: "系统提示",
      content: msg,
      showCancel: false,
      complete: () => {
        wx.navigateBack();
      },
    });
  else wx.showToast({ title: msg, icon: "error" });
}

// 缓存最后一次验证的时间和结果
let lastCheckTime = 0;
let lastCheckResult = null;
const CHECK_CACHE_DURATION = 30000;

// 防止重复登录检查的标志
let isCheckingLogin = false;

// 判断是否登录
export function checkLogin(
  callback,
  showLoading = false,
  forceRefresh = false
) {
  // 防止重复检查
  if (isCheckingLogin) {
    return;
  }

  const now = Date.now();
  const user = wx.getStorageSync("USER");
  const token = wx.getStorageSync("ZY_TOKEN");

  // 如果没有用户信息或token，直接跳转登录
  if (!user || !token) {
    console.log("=== 缺少用户信息或token，跳转登录 ===");
    isCheckingLogin = true;
    setTimeout(() => {
      isCheckingLogin = false;
    }, 2000); // 2秒后重置标志
    return showLoginModal();
  }

  // 如果强制刷新或缓存过期，需要重新验证
  const shouldRefresh =
    forceRefresh ||
    now - lastCheckTime >= CHECK_CACHE_DURATION ||
    !lastCheckResult;

  // 如果在缓存时间内且有缓存结果，且不需要强制刷新，直接返回
  if (!shouldRefresh && lastCheckResult) {
    return callback && callback(lastCheckResult);
  }

  // 只在明确需要时显示loading
  if (showLoading) {
    wx.showLoading({ title: "身份验证中..." });
  }

  wx.checkSession({
    success: function () {
      if (showLoading) wx.hideLoading();

      // 如果有用户信息和token，先尝试直接返回
      if (user && token) {
        lastCheckTime = now;
        lastCheckResult = user;
        return callback && callback(user);
      }

      // 如果需要验证token，进行网络请求
      wx.request({
        method: "POST",
        url: settings.api_host + "/wx/user",
        header: {
          "zy-token": token,
          appId: settings.api_id,
          appSecret: settings.api_secret,
        },
        contentType: "application/json; charset=utf-8",
        data: null,
        dataType: "json",
        success: function (r) {

          if (!r || !r.data || r.data.code < 0 || !r.data.data) {
            lastCheckTime = 0;
            lastCheckResult = null;
            return showLoginModal();
          }

          const validUser = r.data.data;
          lastCheckTime = now;
          lastCheckResult = validUser;

          wx.setStorageSync("ZY_TOKEN", validUser.token);
          return callback && callback(validUser);
        },
        error: function (r) {
          lastCheckTime = 0;
          lastCheckResult = null;
          isCheckingLogin = true;
          setTimeout(() => {
            isCheckingLogin = false;
          }, 2000); // 2秒后重置标志
          return showLoginModal();
        },
      });
    },
    fail: function () {
      if (showLoading) wx.hideLoading();
      //需要执行登录流程
      lastCheckTime = 0;
      lastCheckResult = null;
      isCheckingLogin = true;
      setTimeout(() => {
        isCheckingLogin = false;
      }, 2000); // 2秒后重置标志
      showLoginModal();
    },
  });
}

export function showLoginModal() {
  wx.redirectTo({
    url: "/pages/main/login?url=" + getCurrentPageUrlWithArgs(),
    fail: function () {
      wx.switchTab({
        url: "/pages/tabbar/home/<USER>",
      });
    },
  });
}

// 清理登录状态缓存
export function clearLoginCache() {
  lastCheckTime = 0;
  lastCheckResult = null;
  isCheckingLogin = false;
}

/*获取当前页带参数的url*/
export function getCurrentPageUrlWithArgs() {
  const pages = getCurrentPages(); //获取加载的页面
  const currentPage = pages[pages.length - 1]; //获取当前页面的对象
  const url = currentPage.route; //当前页面url
  const options = currentPage.options; //如果要获取url中所带的参数可以查看options

  //拼接url的参数
  const urlWithArgs = url + "&";
  // let urlWithArgs = url + '?'
  for (const key in options) {
    const value = options[key];
    urlWithArgs += key + "=" + value + "&";
  }
  return urlWithArgs.substring(0, urlWithArgs.length - 1);
}

export function redirect(url) {
  wx.redirectTo({
    url: url || "/page/home/<USER>",
    fail: function () {
      wx.switchTab({
        url: url || "/page/home/<USER>",
      });
    },
  });
}

export function getLocation(callback) {
  wx.getSetting({
    success(res) {
      if (!res.authSetting["scope.userLocation"]) {
        // 首次请求定位权限
        wx.authorize({
          scope: "scope.userLocation",
          success() {
            getUserLocation(callback);
          },
          fail() {
            // 提示去设置页面开启
            wx.showModal({
              title: '定位权限申请',
              content: '扫码定位功能需要获取您的地理位置，请在设置中开启定位权限',
              confirmText: '去设置',
              cancelText: '取消',
              success(modalRes) {
                if (modalRes.confirm) {
                  wx.openSetting({
                    success(settingRes) {
                      if (settingRes.authSetting["scope.userLocation"]) {
                        getUserLocation(callback);
                      } else {
                        error("需要定位权限");
                      }
                    },
                    fail() {
                      error("无法打开设置");
                    }
                  });
                } else {
                  error("需要定位权限");
                }
              }
            });
          }
        });
      } else if (res.authSetting["scope.userLocation"] === false) {
        // 权限被拒绝，提示去设置
        wx.showModal({
          title: '定位权限申请',
          content: '扫码定位功能需要获取您的地理位置，请在设置中开启定位权限',
          confirmText: '去设置',
          cancelText: '取消',
          success(modalRes) {
            if (modalRes.confirm) {
              wx.openSetting({
                success(settingRes) {
                  if (settingRes.authSetting["scope.userLocation"]) {
                    getUserLocation(callback);
                  } else {
                    error("需要定位权限");
                  }
                },
                fail() {
                  error("无法打开设置页面");
                }
              });
            } else {
              error("需要定位权限");
            }
          }
        });
      } else {
        // 已有权限，直接获取定位
        getUserLocation(callback);
      }
    },
    fail() {
      error("设备不支持定位");
    },
  });
}

export function getUserLocation(callback) {
  wx.getLocation({
    type: "gcj02",
    success(res) {
      callback && callback(res);
    },
    fail() {
      error("无法定位");
    },
  });
}

export function getLocationAddress(req) {
  wx.request({
    url: "https://restapi.amap.com/v3/geocode/regeo",
    data: {
      key: settings.amap_key,
      location: req.lng + "," + req.lat,
      extensions: "all",
      s: "rsx",
      sdkversion: "sdkversion",
      logversion: "logversion",
    },
    success: function (res) {
      if (res.data && res.data.info == "OK" && req.callback)
        req.callback(res.data.regeocode);
    },
    fail: function (res) { },
  });
}

export function getPointText(point) {
  point = parseInt(point) - 1 || 0;
  if (point < 0) point = 0;
  return ["非常不满意", "不满意", "一般", "满意", "非常满意"][point] || "";
}
