zy:
  name: "基于物联网彩票设备管理平台"
  author: "<PERSON><PERSON>"
  url: "http://localhost"
  timeout: 120 #session超时（分钟）
  job: true # 任务用标志
  rootDept: 'A' # 省中心编码
  zdj: "0101" # 终端机类型编码
  #  password: "1DD6113ADDE731AB"  # 创建用户默认密码
  password: "94A0B72F0615076C8824C3EAB1B2FFF9"  # 创建用户默认密码
  super-admin-name: "ticai"
  dept:
    maintain: "A80"  # 业主单位，根级单位ID
    consumer: "A90"  # 业主单位，根级单位ID
#  sms:
#    domain: "dysmsapi.aliyuncs.com"
#    accessKeyId: "LTAI5tSbyrdRAuVXqsqcpUbd"
#    accessKeySecret: "******************************"
#    woSign: "海南体彩设备"
#    woCode: "SMS_236875738" # 工单模板编号，工单号：152418334，参数：code
  sms:
    domain: "dysmsapi.aliyuncs.com"
    accessKeyId: "LTAI5tLVwFVnB9shckJQsxGK"
    accessKeySecret: "******************************"
    woSign: "海南省体育彩票管理中心"
    woCode: "SMS_491140010" # 工单模板编号，参数：code

  no:               #编码规则类型定义
    asset: ASSET    #资产
server:
  port: 8080
  servlet:
    context-path: /api
  # 隐藏服务器版本信息
  server-header: ""
  error:
    # 禁用错误页面上的异常信息
    include-exception: false
    include-stacktrace: never
    include-message: never
    # 自定义错误页面路径
    path: /error
spring:
  main:
    banner-mode: "off"
    allow-bean-definition-overriding: true
  application:
    name: ia
  mvc:
    throw-exception-if-no-handler-found: true
  servlet:
    multipart:
      max-file-size: 128MB
      max-request-size: 256MB
    session:
      cookie:
        secure: true
        http-only: true
  profiles:
    active: zl
  web:
    resources:
      add-mappings: false
  # 禁用不必要的端点
  jmx:
    enabled: false

# 管理端点安全配置
management:
  endpoints:
    enabled-by-default: false
    web:
      exposure:
        include: health
  endpoint:
    health:
      enabled: true
      show-details: never

#mybatis中mapper配置文件位置扫描
mybatis:
  config-location: classpath:mybatis-mysql.xml
  mapper-locations: classpath:**/dao/mapper/mysql/**/*.xml

jasper:
  path: .