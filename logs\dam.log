07-21 11:51:15.927 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 9440 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
07-21 11:51:15.928 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-21 11:51:15.930 [main] INFO  com.zy.dam.DamApplication - No active profile set, falling back to 1 default profile: "default"
07-21 11:51:16.584 [main] WARN  o.s.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.support.BeanDefinitionOverrideException: Invalid bean definition with name 'securityHeadersFilter' defined in class path resource [com/zy/config/SecurityConfig.class]: Cannot register bean definition [Root bean: class [null]; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=securityConfig; factoryMethodName=securityHeadersFilter; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/zy/config/SecurityConfig.class]] for bean 'securityHeadersFilter': There is already [Generic bean: class [com.zy.config.SecurityHeadersFilter]; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null; defined in file [F:\work\ticai\ticai-dam\target\classes\com\zy\config\SecurityHeadersFilter.class]] bound.
07-21 11:51:16.591 [main] INFO  o.s.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
07-21 11:51:16.602 [main] ERROR org.springframework.boot.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

The bean 'securityHeadersFilter', defined in class path resource [com/zy/config/SecurityConfig.class], could not be registered. A bean with that name has already been defined in file [F:\work\ticai\ticai-dam\target\classes\com\zy\config\SecurityHeadersFilter.class] and overriding is disabled.

Action:

Consider renaming one of the beans or enabling overriding by setting spring.main.allow-bean-definition-overriding=true

07-21 11:51:32.523 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 12552 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
07-21 11:51:32.523 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-21 11:51:32.525 [main] INFO  com.zy.dam.DamApplication - No active profile set, falling back to 1 default profile: "default"
07-21 11:51:32.945 [main] WARN  o.s.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.support.BeanDefinitionOverrideException: Invalid bean definition with name 'securityHeadersFilter' defined in class path resource [com/zy/config/SecurityConfig.class]: Cannot register bean definition [Root bean: class [null]; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=securityConfig; factoryMethodName=securityHeadersFilter; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/zy/config/SecurityConfig.class]] for bean 'securityHeadersFilter': There is already [Generic bean: class [com.zy.config.SecurityHeadersFilter]; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null; defined in file [F:\work\ticai\ticai-dam\target\classes\com\zy\config\SecurityHeadersFilter.class]] bound.
07-21 11:51:32.952 [main] INFO  o.s.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
07-21 11:51:32.960 [main] ERROR org.springframework.boot.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

The bean 'securityHeadersFilter', defined in class path resource [com/zy/config/SecurityConfig.class], could not be registered. A bean with that name has already been defined in file [F:\work\ticai\ticai-dam\target\classes\com\zy\config\SecurityHeadersFilter.class] and overriding is disabled.

Action:

Consider renaming one of the beans or enabling overriding by setting spring.main.allow-bean-definition-overriding=true

07-21 11:54:25.143 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 33900 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
07-21 11:54:25.143 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-21 11:54:25.145 [main] INFO  com.zy.dam.DamApplication - No active profile set, falling back to 1 default profile: "default"
07-21 11:54:25.578 [main] WARN  o.s.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.support.BeanDefinitionOverrideException: Invalid bean definition with name 'securityHeadersFilter' defined in class path resource [com/zy/config/SecurityConfig.class]: Cannot register bean definition [Root bean: class [null]; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=securityConfig; factoryMethodName=securityHeadersFilter; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/zy/config/SecurityConfig.class]] for bean 'securityHeadersFilter': There is already [Generic bean: class [com.zy.config.SecurityHeadersFilter]; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null; defined in file [F:\work\ticai\ticai-dam\target\classes\com\zy\config\SecurityHeadersFilter.class]] bound.
07-21 11:54:25.584 [main] INFO  o.s.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
07-21 11:54:25.592 [main] ERROR org.springframework.boot.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

The bean 'securityHeadersFilter', defined in class path resource [com/zy/config/SecurityConfig.class], could not be registered. A bean with that name has already been defined in file [F:\work\ticai\ticai-dam\target\classes\com\zy\config\SecurityHeadersFilter.class] and overriding is disabled.

Action:

Consider renaming one of the beans or enabling overriding by setting spring.main.allow-bean-definition-overriding=true

07-21 11:56:15.900 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-21 11:56:15.903 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 12132 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
07-21 11:56:15.904 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
07-21 11:56:16.499 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07-21 11:56:16.501 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
07-21 11:56:16.523 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
07-21 11:56:16.694 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=e581c5bc-e588-3c7b-a240-b47499985897
07-21 11:56:16.966 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
07-21 11:56:16.974 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
07-21 11:56:16.975 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
07-21 11:56:16.975 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
07-21 11:56:17.135 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
07-21 11:56:17.135 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1205 ms
07-21 11:56:17.165 [main] INFO  com.zy.config.SessionConfig - Session Cookie配置已应用 - Profile: zl, Secure: false
07-21 11:56:17.233 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
07-21 11:56:17.687 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
07-21 11:56:18.607 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查开始 ===
07-21 11:56:18.607 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM默认时区: Asia/Shanghai
07-21 11:56:18.615 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区显示名称: 中国标准时间
07-21 11:56:18.615 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区偏移量: 8 小时
07-21 11:56:18.615 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 系统默认时区ID: Asia/Shanghai
07-21 11:56:18.615 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 当前JVM时间: 2025-07-21T11:56:18.615810900+08:00[Asia/Shanghai]
07-21 11:56:18.665 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库系统时区: UTC
07-21 11:56:18.682 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库会话时区: SYSTEM
07-21 11:56:18.698 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库全局时区: SYSTEM
07-21 11:56:18.715 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库当前时间: 2025-07-21T03:56:18
07-21 11:56:18.715 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库UTC时间: 2025-07-21T03:56:18
07-21 11:56:18.735 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时间戳: 1753070178716
07-21 11:56:18.735 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库时间戳: 1753070178000
07-21 11:56:18.735 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 时间差异: 716 毫秒
07-21 11:56:18.735 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - ✅ JVM和数据库时间基本一致
07-21 11:56:18.735 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查结束 ===
07-21 11:56:19.411 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
07-21 11:56:19.417 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
07-21 11:56:19.418 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
07-21 11:56:19.418 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
07-21 11:56:19.418 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

07-21 11:56:19.419 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
07-21 11:56:19.419 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
07-21 11:56:19.419 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@38213efa
07-21 11:56:20.648 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
07-21 11:56:20.654 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
07-21 11:56:20.699 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
07-21 11:56:20.739 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
07-21 11:56:20.761 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
07-21 11:56:21.965 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
07-21 11:56:21.965 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
07-21 11:56:21.965 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
07-21 11:56:21.975 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 7.555 seconds (JVM running for 8.287)
07-21 11:56:36.997 [http-nio-20000-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
07-21 11:56:36.998 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
07-21 11:56:36.999 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
07-21 11:56:57.250 [http-nio-20000-exec-3] WARN  org.apache.catalina.util.SessionIdGeneratorBase - Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [143] milliseconds.
07-21 11:56:57.339 [http-nio-20000-exec-3] ERROR org.springframework.web.servlet.HandlerExecutionChain - HandlerInterceptor.afterCompletion threw exception
java.lang.NullPointerException: null
	at com.zy.config.SessionConfig$CookieSecurityResponseWrapper.setHeader(SessionConfig.java:110)
	at com.zy.config.CookieSecurityInterceptor.afterCompletion(CookieSecurityInterceptor.java:32)
	at org.springframework.web.servlet.HandlerExecutionChain.triggerAfterCompletion(HandlerExecutionChain.java:178)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1163)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1084)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SecurityHeadersFilter.doFilter(SecurityHeadersFilter.java:88)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SessionConfig$CookieSecurityFilter.doFilter(SessionConfig.java:79)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:889)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
07-21 11:57:00.093 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 11:57:00.111 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 11:57:00.127 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 11:57:00.143 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 11:57:17.358 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-21 11:57:17.532 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
07-21 11:57:17.532 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
07-21 11:57:17.532 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-21 11:57:17.533 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
07-21 11:57:17.657 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
07-21 11:57:17.838 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
07-21 14:39:28.056 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 27888 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
07-21 14:39:28.056 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-21 14:39:28.058 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
07-21 14:39:28.835 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07-21 14:39:28.836 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
07-21 14:39:28.860 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
07-21 14:39:29.034 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=e581c5bc-e588-3c7b-a240-b47499985897
07-21 14:39:29.330 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
07-21 14:39:29.340 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
07-21 14:39:29.340 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
07-21 14:39:29.340 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
07-21 14:39:29.545 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
07-21 14:39:29.546 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1456 ms
07-21 14:39:29.597 [main] INFO  com.zy.config.SessionConfig - Session Cookie配置已应用 - Profile: zl, Secure: false
07-21 14:39:29.688 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
07-21 14:39:30.197 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
07-21 14:39:31.188 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查开始 ===
07-21 14:39:31.188 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM默认时区: Asia/Shanghai
07-21 14:39:31.196 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区显示名称: 中国标准时间
07-21 14:39:31.196 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区偏移量: 8 小时
07-21 14:39:31.196 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 系统默认时区ID: Asia/Shanghai
07-21 14:39:31.196 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 当前JVM时间: 2025-07-21T14:39:31.196108600+08:00[Asia/Shanghai]
07-21 14:39:31.261 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库系统时区: UTC
07-21 14:39:31.282 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库会话时区: SYSTEM
07-21 14:39:31.302 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库全局时区: SYSTEM
07-21 14:39:31.322 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库当前时间: 2025-07-21T06:39:31
07-21 14:39:31.322 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库UTC时间: 2025-07-21T06:39:31
07-21 14:39:31.345 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时间戳: 1753079971322
07-21 14:39:31.345 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库时间戳: 1753079972000
07-21 14:39:31.345 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 时间差异: 678 毫秒
07-21 14:39:31.345 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - ✅ JVM和数据库时间基本一致
07-21 14:39:31.345 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查结束 ===
07-21 14:39:32.163 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
07-21 14:39:32.174 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
07-21 14:39:32.174 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
07-21 14:39:32.174 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
07-21 14:39:32.175 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

07-21 14:39:32.175 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
07-21 14:39:32.175 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
07-21 14:39:32.175 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@619b7436
07-21 14:39:32.400 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
07-21 14:39:32.459 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
07-21 14:39:32.517 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
07-21 14:39:32.537 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
07-21 14:39:32.711 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
07-21 14:39:32.712 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
07-21 14:39:32.722 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 6.24 seconds (JVM running for 7.922)
07-21 14:39:39.513 [http-nio-20000-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
07-21 14:39:39.513 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
07-21 14:39:39.514 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
07-21 14:40:00.077 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:40:00.098 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:40:00.118 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:40:00.140 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:40:02.673 [http-nio-20000-exec-9] WARN  org.apache.catalina.util.SessionIdGeneratorBase - Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [136] milliseconds.
07-21 14:40:02.768 [http-nio-20000-exec-9] ERROR org.springframework.web.servlet.HandlerExecutionChain - HandlerInterceptor.afterCompletion threw exception
java.lang.NullPointerException: null
	at com.zy.config.SessionConfig$CookieSecurityResponseWrapper.setHeader(SessionConfig.java:110)
	at com.zy.config.CookieSecurityInterceptor.afterCompletion(CookieSecurityInterceptor.java:32)
	at org.springframework.web.servlet.HandlerExecutionChain.triggerAfterCompletion(HandlerExecutionChain.java:178)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1163)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1084)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SecurityHeadersFilter.doFilter(SecurityHeadersFilter.java:88)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SessionConfig$CookieSecurityFilter.doFilter(SessionConfig.java:79)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:889)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
07-21 14:41:00.077 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:41:00.098 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:41:00.118 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:41:00.140 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:42:00.081 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:42:00.102 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:42:00.122 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:42:00.150 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:43:00.065 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:43:00.086 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:43:00.108 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:43:00.130 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:44:00.083 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:44:00.104 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:44:00.125 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:44:00.145 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:45:00.084 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:45:00.105 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:45:00.127 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:45:00.147 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:45:13.388 [http-nio-20000-exec-9] INFO  com.zy.dam.asset.svc.AmAssetTraceRecordSVC - 记录资产退机操作成功，资产ID：be1eaa6c-62ad-11f0-8c5c-0242ac1c0008
07-21 14:45:13.389 [http-nio-20000-exec-9] INFO  com.zy.dam.asset.svc.AmAssetBackSVC - 记录设备退机操作，资产ID：be1eaa6c-62ad-11f0-8c5c-0242ac1c0008，终端号：null，网点ID：null
07-21 14:46:00.066 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:46:00.087 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:46:00.108 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:46:00.133 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:47:00.076 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:47:00.097 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:47:00.118 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:47:00.139 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:48:00.109 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:48:00.138 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:48:00.166 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:48:00.194 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:49:00.096 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:49:00.124 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:49:00.152 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:49:00.180 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:49:07.136 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-21 14:49:07.332 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
07-21 14:49:07.332 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
07-21 14:49:07.332 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-21 14:49:07.332 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
07-21 14:49:07.468 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
07-21 14:49:07.974 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
07-21 14:49:13.106 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-21 14:49:13.108 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 26628 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
07-21 14:49:13.110 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
07-21 14:49:13.739 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07-21 14:49:13.740 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
07-21 14:49:13.759 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
07-21 14:49:13.941 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=e581c5bc-e588-3c7b-a240-b47499985897
07-21 14:49:14.233 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
07-21 14:49:14.241 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
07-21 14:49:14.242 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
07-21 14:49:14.242 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
07-21 14:49:14.345 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
07-21 14:49:14.345 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1211 ms
07-21 14:49:14.375 [main] INFO  com.zy.config.SessionConfig - Session Cookie配置已应用 - Profile: zl, Secure: false
07-21 14:49:14.423 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
07-21 14:49:15.001 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
07-21 14:49:15.847 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查开始 ===
07-21 14:49:15.847 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM默认时区: Asia/Shanghai
07-21 14:49:15.852 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区显示名称: 中国标准时间
07-21 14:49:15.852 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区偏移量: 8 小时
07-21 14:49:15.852 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 系统默认时区ID: Asia/Shanghai
07-21 14:49:15.853 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 当前JVM时间: 2025-07-21T14:49:15.853335500+08:00[Asia/Shanghai]
07-21 14:49:15.919 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库系统时区: UTC
07-21 14:49:15.944 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库会话时区: SYSTEM
07-21 14:49:15.968 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库全局时区: SYSTEM
07-21 14:49:15.992 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库当前时间: 2025-07-21T06:49:16
07-21 14:49:15.992 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库UTC时间: 2025-07-21T06:49:16
07-21 14:49:16.015 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时间戳: 1753080555992
07-21 14:49:16.015 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库时间戳: 1753080556000
07-21 14:49:16.015 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 时间差异: 8 毫秒
07-21 14:49:16.015 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - ✅ JVM和数据库时间基本一致
07-21 14:49:16.015 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查结束 ===
07-21 14:49:16.667 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
07-21 14:49:16.672 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
07-21 14:49:16.673 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
07-21 14:49:16.673 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
07-21 14:49:16.673 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

07-21 14:49:16.673 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
07-21 14:49:16.673 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
07-21 14:49:16.673 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@29a1dab3
07-21 14:49:17.889 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
07-21 14:49:17.893 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
07-21 14:49:17.931 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
07-21 14:49:17.983 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
07-21 14:49:17.998 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
07-21 14:49:19.191 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
07-21 14:49:19.191 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
07-21 14:49:19.191 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
07-21 14:49:19.200 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 7.594 seconds (JVM running for 8.359)
07-21 14:49:37.048 [http-nio-20000-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
07-21 14:49:37.048 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
07-21 14:49:37.049 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
07-21 14:49:43.798 [http-nio-20000-exec-2] WARN  org.apache.catalina.util.SessionIdGeneratorBase - Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [140] milliseconds.
07-21 14:50:00.098 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:50:00.128 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:50:00.153 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:50:00.178 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:51:00.086 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:51:00.110 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:51:00.142 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:51:00.165 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:51:53.650 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-21 14:51:53.819 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
07-21 14:51:53.819 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
07-21 14:51:53.819 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-21 14:51:53.819 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
07-21 14:51:53.952 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
07-21 14:51:54.212 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
07-21 14:51:58.754 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-21 14:51:58.757 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 19980 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
07-21 14:51:58.758 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
07-21 14:51:59.321 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07-21 14:51:59.323 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
07-21 14:51:59.341 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
07-21 14:51:59.514 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=e581c5bc-e588-3c7b-a240-b47499985897
07-21 14:51:59.822 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
07-21 14:51:59.828 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
07-21 14:51:59.828 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
07-21 14:51:59.829 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
07-21 14:51:59.926 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
07-21 14:51:59.926 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1143 ms
07-21 14:51:59.952 [main] INFO  com.zy.config.SessionConfig - Session Cookie配置已应用 - Profile: zl, Secure: false
07-21 14:52:00.002 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
07-21 14:52:00.506 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
07-21 14:52:01.289 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查开始 ===
07-21 14:52:01.289 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM默认时区: Asia/Shanghai
07-21 14:52:01.294 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区显示名称: 中国标准时间
07-21 14:52:01.294 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区偏移量: 8 小时
07-21 14:52:01.294 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 系统默认时区ID: Asia/Shanghai
07-21 14:52:01.294 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 当前JVM时间: 2025-07-21T14:52:01.294276100+08:00[Asia/Shanghai]
07-21 14:52:01.365 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库系统时区: UTC
07-21 14:52:01.392 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库会话时区: SYSTEM
07-21 14:52:01.415 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库全局时区: SYSTEM
07-21 14:52:01.439 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库当前时间: 2025-07-21T06:52:02
07-21 14:52:01.439 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库UTC时间: 2025-07-21T06:52:02
07-21 14:52:01.464 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时间戳: 1753080721439
07-21 14:52:01.464 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库时间戳: 1753080722000
07-21 14:52:01.464 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 时间差异: 561 毫秒
07-21 14:52:01.464 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - ✅ JVM和数据库时间基本一致
07-21 14:52:01.464 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查结束 ===
07-21 14:52:02.116 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
07-21 14:52:02.121 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
07-21 14:52:02.122 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
07-21 14:52:02.122 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
07-21 14:52:02.123 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

07-21 14:52:02.123 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
07-21 14:52:02.123 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
07-21 14:52:02.123 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@5e75cf7a
07-21 14:52:02.358 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
07-21 14:52:02.409 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
07-21 14:52:02.464 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
07-21 14:52:02.481 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
07-21 14:52:02.671 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
07-21 14:52:02.672 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
07-21 14:52:02.681 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 5.452 seconds (JVM running for 6.188)
07-21 14:53:00.098 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:53:00.122 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:53:00.164 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:53:00.188 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:53:36.571 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-21 14:53:36.714 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
07-21 14:53:36.714 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
07-21 14:53:36.714 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-21 14:53:36.714 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
07-21 14:53:36.739 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
07-21 14:53:36.980 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
07-21 14:55:21.476 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-21 14:55:21.479 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 15856 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
07-21 14:55:21.480 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
07-21 14:55:22.073 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07-21 14:55:22.074 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
07-21 14:55:22.096 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
07-21 14:55:22.327 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=1b44a730-e903-3697-99d9-e7814c6b20b7
07-21 14:55:22.611 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
07-21 14:55:22.617 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
07-21 14:55:22.617 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
07-21 14:55:22.617 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
07-21 14:55:22.712 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
07-21 14:55:22.712 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1208 ms
07-21 14:55:22.739 [main] INFO  com.zy.config.SessionConfig - Session Cookie配置已应用 - Profile: zl, Secure: false
07-21 14:55:22.782 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
07-21 14:55:23.346 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
07-21 14:55:24.847 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
07-21 14:55:24.853 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
07-21 14:55:24.853 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
07-21 14:55:24.853 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
07-21 14:55:24.854 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

07-21 14:55:24.854 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
07-21 14:55:24.854 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
07-21 14:55:24.854 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@74267ece
07-21 14:55:25.074 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
07-21 14:55:25.116 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
07-21 14:55:25.159 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
07-21 14:55:25.175 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
07-21 14:55:25.367 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
07-21 14:55:25.367 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
07-21 14:55:25.376 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 5.464 seconds (JVM running for 6.318)
07-21 14:56:00.097 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:56:00.122 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:56:00.147 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:56:00.170 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:57:00.081 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:57:00.105 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:57:00.130 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:57:00.154 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:58:00.086 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:58:00.111 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:58:00.135 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:58:00.159 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:59:00.083 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:59:00.108 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:59:00.133 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 14:59:00.158 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 15:00:00.074 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 15:00:00.098 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 15:00:00.122 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 15:00:00.147 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 15:01:00.076 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 15:01:00.100 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 15:01:00.124 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 15:01:00.149 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 15:02:00.076 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 15:02:00.101 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 15:02:00.125 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 15:02:00.150 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 15:03:00.077 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 15:03:00.103 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 15:03:00.129 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 15:03:00.153 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
