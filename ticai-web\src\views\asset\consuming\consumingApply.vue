<template>
  <div>
    <el-dialog v-dialog-drag title="领用申请" width="1100px" :visible.sync="visible" :close-on-press-escape="false"
      :close-on-click-modal="false">
      <el-form ref="dataform" size="small" label-width="140px" :model="form" :rules="formRules">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="领用日期：" prop="time">
              <el-date-picker v-model="form.time" type="date" placeholder="请选择领用日期" value-format="yyyy-MM-dd"
                format="yyyy-MM-dd" clearable editable style="width:100%;" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="领用人：" prop="receiver">
              <user-chosen v-model="form.receiver" type="1" clearable placeholder="请选择领用人" style="width:100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="领用部门/区域：" prop="region">
              <dept-region-chosen v-model="deptRegion" :simple="false" clearable placeholder="请选择领用部门/区域"
                style="width:100%" @selected="selectRegion" />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="领用网点：" prop="location">
              <el-select v-model="form.location" filterable :filter-method="searchLocation" clearable placeholder="请选择领用网点" style="width:100%">
                <el-option v-for="item in locationOption" :key="item.id" :label="item.name" :value="item.id">
                  <span style="float: left">{{ item.name }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ item.contact + ', ' + item.phone }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col> -->
          <el-col :span="12">
            <el-form-item label="网点编码:" prop="locationCode">
              <el-input v-model="form.locationCode" readonly class="readonly">
                <el-button slot="append" @click="selectLocation">选择</el-button>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="网点名称:" prop="locationName">
              <el-input v-model="form.locationName" readonly class="readonly">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人:" prop="locationContact">
              <el-input v-model="form.locationContact" readonly class="readonly" style="width:100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="联系电话:" prop="locationPhone">
              <el-input v-model="form.locationPhone" readonly class="readonly">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="网点地址:" prop="locationAddress">
              <el-input v-model="form.locationAddress" readonly class="readonly" style="width:100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="关联网点ID:" prop="location" readonly class="readonly" style="display:none">
              <el-input v-model="form.location" style="width:100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="预计退库日期：" prop="backTime">
              <el-date-picker v-model="form.backTime" type="date" placeholder="请选择预计退库日期" value-format="yyyy-MM-dd"
                format="yyyy-MM-dd" clearable editable style="width:100%;" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="领用说明：" prop="memo">
              <el-input v-model="form.memo" maxlength="200" show-word-limit clearable placeholder="请输入领用说明"
                style="width:100%" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <upload-file v-model="fileList" simple multiple type="LY" />
      <div style="margin-top:20px;">领用资产明细：</div>
      <el-divider></el-divider>
      <el-table :data="assetList" size="small" border>
        <el-table-column label="资产类型" prop="typeName" width="180" header-align="center" />
        <el-table-column label="资产编码" prop="no" width="120" align="center" />
        <el-table-column label="资产名称" prop="name" min-width="150" />
        <el-table-column label="所属部门" prop="deptName" width="150" header-align="center" />
        <el-table-column label="当前状态" prop="status" width="80" align="center">
          <template slot-scope="scope">
            <el-tag :type="getAssetStatusType(scope.row)" size="small">{{ getAssetStatusText(scope.row) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="80">
          <template slot="header">
            <el-button type="success" size="mini" @click="addAsset">新 增</el-button>
          </template>
          <template slot-scope="scope">
            <el-link type="danger" size="mini" icon="el-icon-remove" @click.stop="removeAsset(scope.index)">移除</el-link>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取 消</el-button>
        <el-button type="primary" @click="save">确 定</el-button>
      </div>
    </el-dialog>
    <asset-chosen ref="asset" multiple @selected="selectedAsset" />
    <location-select ref="location" @selected="selectedLocation" />
  </div>
</template>
<script>
import { dateStr } from '@/utils'
import { getAssetStatusType, getAssetStatusText } from '../js/asset.js'
import AssetChosen from '../account/AssetChosen.vue'
import UserChosen from '@/views/components/UserChosen.vue'
import DeptRegionChosen from '@/views/components/DeptRegionChosen.vue'
import UploadFile from '@/views/components/UploadFile.vue'
import LocationSelect from '../../wo/LocationSelect.vue'

export default {
  components: { AssetChosen, UserChosen, DeptRegionChosen, UploadFile, LocationSelect },
  data() {
    const deptRegionRule = (rule, value, callback) => {
      if (!this.form.dept) callback('请选择部门/区域')
      else callback()
    }
    return {
      visible: false,
      currDate: dateStr(),
      form: { receiver: null, location: null }, // 领用申请信息表单
      deptRegion: [],
      locationList: [],
      locationOption: [],
      formRules: {
        time: [{ required: true, message: '请选择领用日期', trigger: 'blur' }],
        receiver: [{ required: true, message: '请选择领用人', trigger: 'blur' }],
        region: [{ required: true, validator: deptRegionRule, trigger: 'blur' }]
        // locationCode: [{ required: true, message: '请选择网点编码', trigger: 'blur' }]
      },
      assetList: [],
      fileList: []
    }
  },
  watch: {
    deptRegion: function (nv) {
      // 保存最后选中的节点ID（即实际选中的部门/区域）
      this.form.dept = nv && nv.length ? nv[nv.length - 1] : null
      // 如果有多级选择，region保存倒数第二级，否则为null
      this.form.region = nv && nv.length > 1 ? nv[nv.length - 2] : null
    }
  },
  methods: {
    getAssetStatusType(v) {
      return getAssetStatusType(v.status)
    },
    getAssetStatusText(v) {
      return getAssetStatusText(v.status)
    },
    show() {
      this.visible = true
      this.form = { time: dateStr(), receiver: null, dept: null, region: null, location: null }
      this.assetList = []
      this.fileList = []
      this.deptRegion = []
    },
    addAsset() {
      this.$refs.asset.show({ statusList: ['1', '7'] })
    },
    selectedAsset(items) {
      const ids = {}
      this.assetList.forEach(r => { ids[r.id] = true })
      items.forEach(r => {
        if (!ids[r.id]) this.assetList.push(r)
      })
    },
    selectRegion(v) {
      this.locationList = []
      this.locationOption = []
      if (v && v.length > 1) {
        this.$http('/am/location/region/' + v[v.length - 1]).then(res => {
          if (res.code > 0) {
            // this.form.location = null
            this.locationList = res.data
            this.locationOption = res.data
          }
        })
      }
    },
    searchLocation(v) {
      if (v) {
        this.locationOption = this.locationList.filter(item => {
          return item.name.indexOf(v) !== -1 || item.phone.indexOf(v) !== -1
        })
      } else this.locationOption = this.locationList
    },
    removeAsset(index) {
      this.$confirm('确定要移除吗?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }).then(() => {
        this.assetList.splice(index, 1)
      }).catch(() => {
      })
    },
    save() {
      this.$refs.dataform.validate(valid => {
        if (valid) {
          const details = []
          this.assetList.forEach(r => details.push({ asset: r.id, dept: r.dept, region: r.region, location: r.location, useDept: r.userDept, useUser: r.useUser, assetStatus: r.status }))
          if (!details.length) return this.$message.warning('请选择要领用的资产')
          this.form.assetList = details
          this.form.fileList = this.fileList
          this.$http({ url: '/am/asset/consuming/apply', data: this.form }).then(res => {
            if (res.code > 0) {
              this.visible = false
              this.$message.success('提交成功')
              this.$emit('success')
            }
          })
        }
      })
    },
    selectLocation() {
      this.$refs.location.show()
    },
    selectedLocation(item) {
      this.form.location = item.id
      this.form.locationCode = item.code
      this.form.locationName = item.name
      this.form.locationContact = item.contact
      this.form.locationPhone = item.phone
      this.form.locationAddress = item.address
    }
  }
}
</script>
