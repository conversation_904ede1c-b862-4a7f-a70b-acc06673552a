<template>
	<view class="container">
		<view>
			<view class="wechatapp">
				<view class="header-avatar">
					<open-data type="userAvatarUrl"></open-data>
				</view>
			</view>
			<view class="auth-title">申请获取以下权限</view>
			<view class="auth-subtitle">获得你的公开信息（昵称、头像等）</view>
			<button v-if="canIUseGetUserProfile" class="login-btn" openType="getUserProfile" lang="zh_CN"
				@click="getUserProfile">{{ info }}授权登录</button>
			<button v-else class="login-btn" openType="getUserInfo" lang="zh_CN" @click="bindGetUserInfo">{{ info
			}}授权登录</button>
			<button class="login-close" lang="zh_CN" @tap="loginClose">取消登录</button>
		</view>
	</view>
</template>

<script>
import * as ctx from '../../utils/context.js'

export default {
	data() {
		return {
			canIUseGetUserProfile: false,
			url: '/pages/home/<USER>',
			openId: '',
			wechatshareopenid: '',
			info: ''
		}
	},
	onLoad(options) {
		if (wx.getUserProfile) {
			this.canIUseGetUserProfile = true
		}
		if (options.url) {
			let url = options.url
			if (url.substring(0, 1) != '/') url = '/' + url
			var qstring
			for (let obj in options) {
				if (obj != "url") {
					if (qstring) qstring += '&' + obj + "=" + options[obj]
					else qstring = '?' + obj + "=" + options[obj]
				}
			}
			if (qstring) url += qstring
			this.url = url
		}
	},
	methods: {
		getUserProfile() {
			const that = this;
			wx.getUserProfile({
				desc: '用于关联业务数据',
				success: (res) => {
					//用户按了允许授权按钮
					that.autoLoginReg(that, res.userInfo)
				}
			})
		},
		bindGetUserInfo: function (e) {
			if (e.detail.userInfo) {
				//用户按了允许授权按钮
				// wx.setStorageSync(app.STORE_WX_USER, ui);//wxUser
				this.autoLoginReg(this, e.detail.UserInfo)
			} else {
				//用户按了拒绝按钮
				wx.showModal({
					title: '警告',
					content: '您点击了拒绝授权，将无法进入小程序，请授权之后再进入!!!',
					showCancel: false,
					confirmText: '返回授权',
					success: function (res) {
						if (res.confirm) {
						}
					}
				})
			}
		},
		autoLoginReg: function (that, ui) {
			let jscode = '';
			wx.login({
				success: function (res) {
					if (res.code) {
						jscode = res.code;
						//2、调用获取用户信息接口
						// wx.getUserInfo({
						//   success: function (res) {
						//     encryptedData: res.encryptedData;
						//     iv:res.iv;
						// var data = { opr: "getcode", jscode: jscode, name: ui.nickName, avatarUrl: ui.avatarUrl, province: ui.province, city: ui.city, encryptedData: res.encryptedData, iv: res.iv, scene: wx.getStorageSync('scene') }
						const data = { opr: "getcode", jscode: jscode, name: ui.nickName, avatarUrl: ui.avatarUrl, province: ui.province, city: ui.city, scene: wx.getStorageSync('scene') }

						// 显示统一的加载提示
						wx.showLoading({ title: '登录中...' })

						ctx.post('/wx/login', data, function (r) {
							wx.hideLoading()
							if (r.code < 0 || !r.data || !r.data.token) return ctx.error(r.msg || '登录失败，请重新尝试');
							wx.setStorageSync('USER', r.data);
							wx.setStorageSync('ZY_TOKEN', r.data.token);
							ctx.redirect(that.url);
						}, function (r) {
							wx.hideLoading()
							console.log(r)
						});
					} else {
						console.log('获取用户登录失败！' + res.errMsg);
					}
				},
				fail: function (res) {
					console.log('wx.login失败：', res);
				}
			});
		},
		loginClose: function () {
			ctx.redirect("/pages/tabbar/home/<USER>")
		}
	}
}
</script>

<style>
.container {
	padding: 0 60rpx;
	background: #fff;
}

.wechatapp {
	padding: 80rpx 0 48rpx;
	border-bottom: 1rpx solid #e3e3e3;
	margin-bottom: 72rpx;
	text-align: center;
}

.wechatapp .header-avatar {
	width: 250rpx;
	height: 250rpx;
	border: 2px solid #fff;
	margin: 0 auto;
	padding: 0;
	border-radius: 50%;
	overflow: hidden;
	box-shadow: 1px 0px 5px rgba(50, 50, 50, 0.3);
}

.auth-title {
	color: #585858;
	font-size: 40rpx;
	margin-bottom: 40rpx;
}

.auth-subtitle {
	color: #888;
	margin-bottom: 88rpx;
}

.login-btn {
	border: none;
	height: 88rpx;
	line-height: 88rpx;
	padding: 0;
	background: #04be01;
	color: #fff;
	border-radius: 999rpx;
}

.login-btn::after {
	display: none;
}

.login-btn.button-hover {
	box-shadow: inset 0 5rpx 30rpx rgba(0, 0, 0, 0.15);
}

.login-close {
	border: none;
	height: 88rpx;
	line-height: 88rpx;
	padding: 0;
	background: #989898;
	color: #fff;
	border-radius: 999rpx;
	margin-top: 10px;
}
</style>
