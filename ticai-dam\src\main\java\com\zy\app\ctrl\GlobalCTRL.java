package com.zy.app.ctrl;

import com.zy.app.svc.GlobalSVC;
import com.zy.model.ValueLabel;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "系统公共接口")
@RestController
public class GlobalCTRL {

    @Resource
    private GlobalSVC svc;


    @Operation(summary = "获取字典清单")
    @PostMapping("/code/{dict}")
    public List<ValueLabel> code(@Parameter(description = "字典类型") @PathVariable("dict") String dict) {
        return svc.findCode(dict);
    }

    @Operation(summary = "下级区划")
    @PostMapping("/region/{code}")
    public List<ValueLabel> region(@Parameter(description = "区划代码") @PathVariable("code") String code) {
        return svc.findRegion(code);
    }

    @Operation(summary = "展示到区划")
    @PostMapping("/regionTo/{code}")
    public List<ValueLabel> regionTo(@Parameter(description = "区划代码") @PathVariable("code") String code) {
        return svc.findRegionTo(code, false);
    }

    @Operation(summary = "展示到区划(包含指定根节点)")
    @PostMapping("/regionWith/{code}")
    public List<ValueLabel> regionWith(@Parameter(description = "区划代码") @PathVariable("code") String code) {
        return svc.findRegionTo(code, true);
    }

}
