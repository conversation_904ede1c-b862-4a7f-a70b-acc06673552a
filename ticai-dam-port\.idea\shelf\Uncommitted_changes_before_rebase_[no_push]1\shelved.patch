Index: src/main/resources/application.yml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>zy:\r\n  name: \"基于物联网彩票设备管理平台\"\r\n  author: \"<PERSON><PERSON>\"\r\n  url: \"http://localhost\"\r\n  timeout: 120 #session超时（分钟）\r\n  job: true # 任务用标志\r\n#  sms:\r\n#    domain: \"dysmsapi.aliyuncs.com\"\r\n#    accessKeyId: \"LTAI5tSbyrdRAuVXqsqcpUbd\"\r\n#    accessKeySecret: \"******************************\"\r\n#    woSign: \"海南体彩设备\"\r\n#    woCode: \"SMS_236875738\" # 工单模板编号，工单号：152253869，参数：code\r\n  sms:\r\n    domain: \"dysmsapi.aliyuncs.com\"\r\n    accessKeyId: \"LTAI5tLVwFVnB9shckJQsxGK\"\r\n    accessKeySecret: \"******************************\"\r\n    woSign: \"海南省体育彩票管理中心\"\r\n    woCode: \"SMS_491140010\" # 工单模板编号，参数：code\r\n  no:               #编码规则类型定义\r\n    asset: ASSET    #资产\r\nserver:\r\n  port: 8080\r\n  servlet:\r\n    context-path: /port-api\r\nspring:\r\n  main:\r\n    banner-mode: \"off\"\r\n    allow-bean-definition-overriding: true\r\n  application:\r\n    name: ia\r\n  mvc:\r\n    throw-exception-if-no-handler-found: true\r\n  servlet:\r\n    multipart:\r\n      max-file-size: 128MB\r\n      max-request-size: 256MB\r\n  profiles:\r\n    active: prod\r\n  web:\r\n    resources:\r\n      add-mappings: false\r\n\r\n#mybatis中mapper配置文件位置扫描\r\nmybatis:\r\n  config-location: classpath:mybatis-mysql.xml\r\n  mapper-locations: classpath:com/zy/**/dao/mapper/mysql/**/*.xml\r\n  \r\njasper:\r\n  path: .\r\n\r\nweixin:\r\n  appid: \"wx6656f02e1b896e38\"\r\n  secret: \"3a82f3ba9391256a7171a3dba2e20929\"\r\n\r\namap:\r\n  key: \"b4ae4625e7f217801a29af656cbf8ce7\"
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/main/resources/application.yml b/src/main/resources/application.yml
--- a/src/main/resources/application.yml	(revision aa7f50cc56a9f774910afc5f08117419d5f8f139)
+++ b/src/main/resources/application.yml	(date 1752666813637)
@@ -35,7 +35,7 @@
       max-file-size: 128MB
       max-request-size: 256MB
   profiles:
-    active: prod
+    active: staging
   web:
     resources:
       add-mappings: false
Index: src/main/resources/application-dev.yml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>server:\r\n  port: 20000\r\nspring:\r\n  datasource-master:\r\n    driver-class-name: com.mysql.cj.jdbc.Driver\r\n    jdbc-url: *************************************************************************************************************************************************    username: xxx\r\n    password: xxxx\r\n    initial-size: 10 #初始化连接\r\n    max-idle: 20 #最大空闲连接\r\n    min-idle: 5 #最小空闲连接\r\n    max-active: 200 #最大连接数量\r\n    log-abandoned: true #是否在自动回收超时连接的时候打印连接的超时错误\r\n    remove-abandoned: true #是否自动回收超时连接\r\n    remove-abandoned-timeout: 180 #超时时间(以秒数为单位)\r\n    max-wait: 10000 #超时等待时间以毫秒为单位 6000毫秒/1000等于60秒\r\n    test-while-idle: true\r\n    connection-test-query: select now()  #检测数据库的查询语句\r\n    test-on-borrow: true\r\n    min-evictable-idle-time-millis: 600000 #每隔五分钟检测空闲超过10分钟的连接\r\n    time-between-eviction-runs-millis: 300000\r\n    maximum-pool-size: 50  #池中最大连接数（包括空闲和正在使用的连接）默认值是10\r\n    minimum-idle: 10  #池中最小空闲连接数量。默认值10\r\n    pool-name: zy-ds-pool  #连接池的名字。\r\n    auto-commit: true  #是否自动提交池中返回的连接。默认值为true\r\n    idle-timeout: 10000  #空闲时间，毫秒\r\n    max-lifetime: 500000 #连接池中连接的最大生命周期。\r\n    connection-timeout: 20000  #连接超时时间，毫秒。默认值为30s\r\n\r\n  redis:\r\n    database: 0 #数据库索引（默认为0）\r\n    host: 127.0.0.1\r\n    port: 6378\r\n    password: xudejian\r\n    pool:\r\n      max-active: 500 #连接池最大连接数（使用负值表示没有限制）\r\n      max-wait: -1 #连接池最大阻塞等待时间（使用负值表示没有限制）\r\n      max-idle: 20 #连接池中的最大空闲连接\r\n      min-idle: 10 #连接池中的最小空闲连接\r\n      timeout: 5000 #连接超时时间（毫秒）\r\n\r\nfile:\r\n  path: E:\\\\Attach\r\n  context: /attach\r\n\r\nlogging:\r\n  level:\r\n    org.springframework.boot.autoconfigure: INFO\r\n\r\nweixin:\r\n  appid: \"wxd8fbf8e2f5fc0a3a\"\r\n  secret: \"65b46f1e57d810989a1040d703668a3b\"
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/main/resources/application-dev.yml b/src/main/resources/application-dev.yml
--- a/src/main/resources/application-dev.yml	(revision aa7f50cc56a9f774910afc5f08117419d5f8f139)
+++ b/src/main/resources/application-dev.yml	(date 1752666813727)
@@ -1,5 +1,5 @@
 server:
-  port: 20000
+  port: 20002
 spring:
   datasource-master:
     driver-class-name: com.mysql.cj.jdbc.Driver
@@ -15,18 +15,24 @@
     remove-abandoned-timeout: 180 #超时时间(以秒数为单位)
     max-wait: 10000 #超时等待时间以毫秒为单位 6000毫秒/1000等于60秒
     test-while-idle: true
-    connection-test-query: select now()  #检测数据库的查询语句
+    connection-test-query: select now() #检测数据库的查询语句
     test-on-borrow: true
     min-evictable-idle-time-millis: 600000 #每隔五分钟检测空闲超过10分钟的连接
     time-between-eviction-runs-millis: 300000
-    maximum-pool-size: 50  #池中最大连接数（包括空闲和正在使用的连接）默认值是10
-    minimum-idle: 10  #池中最小空闲连接数量。默认值10
-    pool-name: zy-ds-pool  #连接池的名字。
-    auto-commit: true  #是否自动提交池中返回的连接。默认值为true
-    idle-timeout: 10000  #空闲时间，毫秒
+    maximum-pool-size: 50 #池中最大连接数（包括空闲和正在使用的连接）默认值是10
+    minimum-idle: 10 #池中最小空闲连接数量。默认值10
+    pool-name: zy-ds-pool #连接池的名字。
+    auto-commit: true #是否自动提交池中返回的连接。默认值为true
+    idle-timeout: 10000 #空闲时间，毫秒
     max-lifetime: 500000 #连接池中连接的最大生命周期。
-    connection-timeout: 20000  #连接超时时间，毫秒。默认值为30s
+    connection-timeout: 20000 #连接超时时间，毫秒。默认值为30s
 
+  servlet:
+    session:
+      cookie:
+        secure: true
+        http-only: true
+
   redis:
     database: 0 #数据库索引（默认为0）
     host: 127.0.0.1
@@ -49,4 +55,4 @@
 
 weixin:
   appid: "wxd8fbf8e2f5fc0a3a"
-  secret: "65b46f1e57d810989a1040d703668a3b"
\ No newline at end of file
+  secret: "65b46f1e57d810989a1040d703668a3b"
Index: src/main/resources/application-staging.yml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>server:\r\n  port: 20002\r\nspring:\r\n  datasource-master:\r\n    driver-class-name: com.mysql.cj.jdbc.Driver\r\n    jdbc-url: *************************************************************************************************************************************************    username: dam\r\n    password: dam!#%531\r\n    initial-size: 10 #初始化连接\r\n    max-idle: 20 #最大空闲连接\r\n    min-idle: 5 #最小空闲连接\r\n    max-active: 200 #最大连接数量\r\n    log-abandoned: true #是否在自动回收超时连接的时候打印连接的超时错误\r\n    remove-abandoned: true #是否自动回收超时连接\r\n    remove-abandoned-timeout: 180 #超时时间(以秒数为单位)\r\n    max-wait: 10000 #超时等待时间以毫秒为单位 6000毫秒/1000等于60秒\r\n    test-while-idle: true\r\n    connection-test-query: select now()  #检测数据库的查询语句\r\n    test-on-borrow: true\r\n    min-evictable-idle-time-millis: 600000 #每隔五分钟检测空闲超过10分钟的连接\r\n    time-between-eviction-runs-millis: 300000\r\n    maximum-pool-size: 50  #池中最大连接数（包括空闲和正在使用的连接）默认值是10\r\n    minimum-idle: 10  #池中最小空闲连接数量。默认值10\r\n    pool-name: zy-ds-pool  #连接池的名字。\r\n    auto-commit: true  #是否自动提交池中返回的连接。默认值为true\r\n    idle-timeout: 10000  #空闲时间，毫秒\r\n    max-lifetime: 500000 #连接池中连接的最大生命周期。\r\n    connection-timeout: 20000  #连接超时时间，毫秒。默认值为30s\r\n\r\n  redis:\r\n    database: 0 #数据库索引（默认为0）\r\n    host: 127.0.0.1\r\n    port: 6379\r\n    password: ZiYuan168\r\n    pool:\r\n      max-active: 500 #连接池最大连接数（使用负值表示没有限制）\r\n      max-wait: -1 #连接池最大阻塞等待时间（使用负值表示没有限制）\r\n      max-idle: 20 #连接池中的最大空闲连接\r\n      min-idle: 10 #连接池中的最小空闲连接\r\n      timeout: 5000 #连接超时时间（毫秒）\r\n\r\nfile:\r\n  path: /data/attach\r\n  context: /attach\r\n\r\nlogging:\r\n  level:\r\n    org.springframework.boot.autoconfigure: INFO\r\n\r\n# 注册中心\r\neureka:\r\n  client:\r\n    enabled: false\r\n    serviceUrl:\r\n      defaultZone: **************************************/eureka/\r\n    tls:\r\n      trust-store-password: abc\r\n  instance:\r\n    leaseRenewalIntervalInSeconds: 10\r\n    metadataMap:\r\n      instanceId: ${vcap.application.instance_id:${spring.application.name}:${spring.application.instance_id:${server.port}}}\r\n\r\n\r\nweixin:\r\n  appid: \"wxc4a8f2bf45bf3ca3\"\r\n  secret: \"65b46f1e57d810989a1040d703668a3b\"\r\n\r\namap:\r\n  key: \"b4ae4625e7f217801a29af656cbf8ce7\"\r\n\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/main/resources/application-staging.yml b/src/main/resources/application-staging.yml
--- a/src/main/resources/application-staging.yml	(revision aa7f50cc56a9f774910afc5f08117419d5f8f139)
+++ b/src/main/resources/application-staging.yml	(date 1752666813736)
@@ -3,9 +3,9 @@
 spring:
   datasource-master:
     driver-class-name: com.mysql.cj.jdbc.Driver
-    jdbc-url: *********************************************************************************************************************************************
-    username: dam
-    password: dam!#%531
+    jdbc-url: **************************************************************************************************************************************************
+    username: root
+    password: XX2U5nxQYeMyQkOA!!
     initial-size: 10 #初始化连接
     max-idle: 20 #最大空闲连接
     min-idle: 5 #最小空闲连接
@@ -15,23 +15,29 @@
     remove-abandoned-timeout: 180 #超时时间(以秒数为单位)
     max-wait: 10000 #超时等待时间以毫秒为单位 6000毫秒/1000等于60秒
     test-while-idle: true
-    connection-test-query: select now()  #检测数据库的查询语句
+    connection-test-query: select now() #检测数据库的查询语句
     test-on-borrow: true
     min-evictable-idle-time-millis: 600000 #每隔五分钟检测空闲超过10分钟的连接
     time-between-eviction-runs-millis: 300000
-    maximum-pool-size: 50  #池中最大连接数（包括空闲和正在使用的连接）默认值是10
-    minimum-idle: 10  #池中最小空闲连接数量。默认值10
-    pool-name: zy-ds-pool  #连接池的名字。
-    auto-commit: true  #是否自动提交池中返回的连接。默认值为true
-    idle-timeout: 10000  #空闲时间，毫秒
+    maximum-pool-size: 50 #池中最大连接数（包括空闲和正在使用的连接）默认值是10
+    minimum-idle: 10 #池中最小空闲连接数量。默认值10
+    pool-name: zy-ds-pool #连接池的名字。
+    auto-commit: true #是否自动提交池中返回的连接。默认值为true
+    idle-timeout: 10000 #空闲时间，毫秒
     max-lifetime: 500000 #连接池中连接的最大生命周期。
-    connection-timeout: 20000  #连接超时时间，毫秒。默认值为30s
+    connection-timeout: 20000 #连接超时时间，毫秒。默认值为30s
 
+  servlet:
+    session:
+      cookie:
+        secure: true
+        http-only: true
+
   redis:
     database: 0 #数据库索引（默认为0）
     host: 127.0.0.1
     port: 6379
-    password: ZiYuan168
+    #    password: ZiYuan168
     pool:
       max-active: 500 #连接池最大连接数（使用负值表示没有限制）
       max-wait: -1 #连接池最大阻塞等待时间（使用负值表示没有限制）
@@ -60,11 +66,9 @@
     metadataMap:
       instanceId: ${vcap.application.instance_id:${spring.application.name}:${spring.application.instance_id:${server.port}}}
 
-
 weixin:
   appid: "wxc4a8f2bf45bf3ca3"
   secret: "65b46f1e57d810989a1040d703668a3b"
 
 amap:
   key: "b4ae4625e7f217801a29af656cbf8ce7"
-
